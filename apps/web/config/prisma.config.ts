import "dotenv/config";
import path from "node:path";
import { defineConfig } from "prisma/config";

// Robust console logging for debugging
console.log("🔧 Loading Prisma configuration...");
console.log(`📁 Schema path: ${path.join("prisma", "schema")}`);
console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
console.log(`🗄️ Database URL configured: ${!!process.env.DATABASE_URL}`);

export default defineConfig({
  earlyAccess: true,
  schema: path.join("prisma", "schema"),
});
