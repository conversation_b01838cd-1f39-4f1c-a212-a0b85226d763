#!/usr/bin/env tsx
/**
 * Database Index Optimization Script
 * 
 * Analyzes current database performance and applies optimized indexes
 * for common query patterns in the BuddyChip application.
 */

import { PrismaClient } from "../../prisma/generated";
import { createPrismaClient } from "../../src/lib/prisma-config";

const prisma = createPrismaClient({
  instanceId: "index-optimizer",
  forceQueryLogs: true,
});

interface IndexOptimization {
  name: string;
  table: string;
  columns: string[];
  type?: "btree" | "gin" | "gist" | "hash";
  where?: string;
  description: string;
  priority: "high" | "medium" | "low";
}

const OPTIMIZED_INDEXES: IndexOptimization[] = [
  // High Priority - Critical for performance
  {
    name: "idx_mentions_user_date_optimized",
    table: "mentions",
    columns: ["userId", "mentionedAt DESC", "archived"],
    description: "Optimized index for user mentions with date sorting and archive filtering",
    priority: "high",
  },
  {
    name: "idx_mentions_account_date_optimized",
    table: "mentions",
    columns: ["accountId", "mentionedAt DESC", "processed"],
    description: "Optimized index for account mentions with processing status",
    priority: "high",
  },
  {
    name: "idx_usage_log_rate_limiting",
    table: "UsageLog",
    columns: ["userId", "feature", "billingPeriod", "createdAt DESC"],
    description: "Critical index for rate limiting queries",
    priority: "high",
  },
  {
    name: "idx_ai_responses_mention_latest",
    table: "AIResponse",
    columns: ["mentionId", "createdAt DESC"],
    description: "Index for fetching latest AI responses per mention",
    priority: "high",
  },

  // Medium Priority - Performance improvements
  {
    name: "idx_mentions_bullish_score_filtered",
    table: "mentions",
    columns: ["bullishScore DESC"],
    where: "bullishScore IS NOT NULL AND bullishScore > 0",
    description: "Partial index for bullish score queries (non-null values only)",
    priority: "medium",
  },
  {
    name: "idx_mentions_content_search",
    table: "mentions",
    columns: ["content"],
    type: "gin",
    description: "GIN index for full-text search on mention content",
    priority: "medium",
  },
  {
    name: "idx_mentions_keywords_search",
    table: "mentions",
    columns: ["keywords"],
    type: "gin",
    description: "GIN index for keyword array searches",
    priority: "medium",
  },
  {
    name: "idx_monitored_accounts_user_active",
    table: "MonitoredAccount",
    columns: ["userId", "isActive", "lastSyncAt DESC"],
    description: "Index for active account queries with sync status",
    priority: "medium",
  },

  // Low Priority - Nice to have optimizations
  {
    name: "idx_memories_user_relevance",
    table: "Memory",
    columns: ["userId", "relevanceScore DESC", "lastAccessedAt DESC"],
    description: "Index for memory relevance and access patterns",
    priority: "low",
  },
  {
    name: "idx_crypto_cache_trending",
    table: "CryptoTrendingCache",
    columns: ["timeframe", "mindshare DESC", "updatedAt DESC"],
    description: "Index for crypto trending queries",
    priority: "low",
  },
];

async function analyzeCurrentIndexes() {
  console.log("🔍 Analyzing current database indexes...");
  
  try {
    const indexes = await prisma.$queryRaw<Array<{
      schemaname: string;
      tablename: string;
      indexname: string;
      indexdef: string;
    }>>`
      SELECT schemaname, tablename, indexname, indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname
    `;

    console.log(`📊 Found ${indexes.length} existing indexes`);
    
    // Group by table
    const indexesByTable = indexes.reduce((acc, index) => {
      if (!acc[index.tablename]) {
        acc[index.tablename] = [];
      }
      acc[index.tablename].push(index);
      return acc;
    }, {} as Record<string, typeof indexes>);

    return indexesByTable;
  } catch (error) {
    console.error("❌ Failed to analyze indexes:", error);
    return {};
  }
}

async function checkIndexExists(indexName: string): Promise<boolean> {
  try {
    const result = await prisma.$queryRaw<Array<{ exists: boolean }>>`
      SELECT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = ${indexName}
      ) as exists
    `;
    return result[0]?.exists || false;
  } catch (error) {
    console.error(`❌ Failed to check if index ${indexName} exists:`, error);
    return false;
  }
}

async function createIndex(optimization: IndexOptimization): Promise<boolean> {
  const { name, table, columns, type = "btree", where, description } = optimization;
  
  try {
    console.log(`🔨 Creating index: ${name}`);
    console.log(`   Description: ${description}`);
    
    // Build the CREATE INDEX statement
    let sql = `CREATE INDEX CONCURRENTLY "${name}" ON "${table}"`;
    
    if (type !== "btree") {
      sql += ` USING ${type}`;
    }
    
    sql += ` (${columns.join(", ")})`;
    
    if (where) {
      sql += ` WHERE ${where}`;
    }

    console.log(`   SQL: ${sql}`);
    
    await prisma.$executeRawUnsafe(sql);
    console.log(`✅ Successfully created index: ${name}`);
    return true;
  } catch (error: any) {
    if (error.message?.includes("already exists")) {
      console.log(`ℹ️ Index ${name} already exists, skipping`);
      return true;
    }
    
    console.error(`❌ Failed to create index ${name}:`, error.message);
    return false;
  }
}

async function analyzeQueryPerformance() {
  console.log("📈 Analyzing query performance...");
  
  try {
    // Check for slow queries
    const slowQueries = await prisma.$queryRaw<Array<{
      query: string;
      calls: number;
      mean_exec_time: number;
      total_exec_time: number;
    }>>`
      SELECT 
        query,
        calls,
        mean_exec_time,
        total_exec_time
      FROM pg_stat_statements 
      WHERE mean_exec_time > 100
      ORDER BY mean_exec_time DESC 
      LIMIT 10
    `;

    if (slowQueries.length > 0) {
      console.log("🐌 Slow queries detected:");
      slowQueries.forEach((query, index) => {
        console.log(`${index + 1}. Mean time: ${query.mean_exec_time.toFixed(2)}ms, Calls: ${query.calls}`);
        console.log(`   Query: ${query.query.substring(0, 100)}...`);
      });
    } else {
      console.log("✅ No slow queries detected");
    }

    return slowQueries;
  } catch (error) {
    console.error("❌ Failed to analyze query performance:", error);
    return [];
  }
}

async function analyzeTableStats() {
  console.log("📊 Analyzing table statistics...");
  
  try {
    const stats = await prisma.$queryRaw<Array<{
      table_name: string;
      seq_scan: number;
      idx_scan: number;
      n_tup_ins: number;
      n_tup_upd: number;
      n_tup_del: number;
    }>>`
      SELECT 
        relname as table_name,
        seq_scan,
        idx_scan,
        n_tup_ins,
        n_tup_upd,
        n_tup_del
      FROM pg_stat_user_tables 
      ORDER BY seq_scan DESC
      LIMIT 10
    `;

    console.log("📈 Table statistics (top 10 by sequential scans):");
    stats.forEach((stat, index) => {
      const totalScans = stat.seq_scan + stat.idx_scan;
      const seqScanRatio = totalScans > 0 ? (stat.seq_scan / totalScans * 100).toFixed(1) : "0";
      
      console.log(`${index + 1}. ${stat.table_name}:`);
      console.log(`   Sequential scans: ${stat.seq_scan} (${seqScanRatio}% of total)`);
      console.log(`   Index scans: ${stat.idx_scan}`);
      console.log(`   Inserts: ${stat.n_tup_ins}, Updates: ${stat.n_tup_upd}, Deletes: ${stat.n_tup_del}`);
    });

    return stats;
  } catch (error) {
    console.error("❌ Failed to analyze table stats:", error);
    return [];
  }
}

async function optimizeIndexes(priority: "high" | "medium" | "low" | "all" = "all") {
  console.log(`🚀 Starting database index optimization (priority: ${priority})...`);
  
  // Filter optimizations by priority
  const optimizations = priority === "all" 
    ? OPTIMIZED_INDEXES 
    : OPTIMIZED_INDEXES.filter(opt => opt.priority === priority);

  console.log(`📋 Planning to create ${optimizations.length} indexes`);

  let created = 0;
  let skipped = 0;
  let failed = 0;

  for (const optimization of optimizations) {
    const exists = await checkIndexExists(optimization.name);
    
    if (exists) {
      console.log(`⏭️ Index ${optimization.name} already exists, skipping`);
      skipped++;
      continue;
    }

    const success = await createIndex(optimization);
    if (success) {
      created++;
    } else {
      failed++;
    }

    // Small delay between index creations to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log("\n🎉 Index optimization completed!");
  console.log(`✅ Created: ${created}`);
  console.log(`⏭️ Skipped: ${skipped}`);
  console.log(`❌ Failed: ${failed}`);

  return { created, skipped, failed };
}

async function main() {
  console.log("🔧 BuddyChip Database Index Optimization");
  console.log("========================================\n");

  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`;
    console.log("✅ Database connection successful\n");

    // Analyze current state
    await analyzeCurrentIndexes();
    await analyzeQueryPerformance();
    await analyzeTableStats();

    console.log("\n" + "=".repeat(50) + "\n");

    // Get priority from command line args
    const priority = (process.argv[2] as "high" | "medium" | "low" | "all") || "all";
    
    // Apply optimizations
    const result = await optimizeIndexes(priority);

    console.log("\n" + "=".repeat(50));
    console.log("🎯 Optimization Summary:");
    console.log(`   Priority level: ${priority}`);
    console.log(`   Indexes created: ${result.created}`);
    console.log(`   Indexes skipped: ${result.skipped}`);
    console.log(`   Indexes failed: ${result.failed}`);

    if (result.created > 0) {
      console.log("\n💡 Recommendations:");
      console.log("   - Monitor query performance over the next few days");
      console.log("   - Run ANALYZE on affected tables to update statistics");
      console.log("   - Consider running this script periodically as data grows");
    }

  } catch (error) {
    console.error("💥 Optimization failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the optimization
if (require.main === module) {
  main().catch(console.error);
}

export { optimizeIndexes, analyzeCurrentIndexes, analyzeQueryPerformance };
