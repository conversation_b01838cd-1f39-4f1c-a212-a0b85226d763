#!/usr/bin/env node

/**
 * Telegram Webhook Setup Script
 * 
 * This script helps set up the Telegram webhook correctly
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const WEBHOOK_URL = 'https://www.buddychip.app/api/telegram/webhook';

function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(data);
    }

    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve(parsed);
        } catch (e) {
          resolve(body);
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    
    req.end();
  });
}

async function main() {
  console.log('🤖 Telegram Webhook Setup\n');

  try {
    // 1. Check current webhook status
    console.log('1️⃣ Checking current webhook status...');
    const webhookInfo = await makeRequest(`https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo`);
    console.log('Current webhook info:', JSON.stringify(webhookInfo, null, 2));

    // 2. Delete existing webhook
    console.log('\n2️⃣ Deleting existing webhook...');
    const deleteResult = await makeRequest(`https://api.telegram.org/bot${BOT_TOKEN}/deleteWebhook`, 'POST');
    console.log('Delete result:', JSON.stringify(deleteResult, null, 2));

    // 3. Set new webhook
    console.log('\n3️⃣ Setting new webhook...');
    const setWebhookData = JSON.stringify({
      url: WEBHOOK_URL,
      allowed_updates: ['message', 'callback_query']
    });
    
    const setResult = await makeRequest(
      `https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`,
      'POST',
      setWebhookData
    );
    console.log('Set webhook result:', JSON.stringify(setResult, null, 2));

    // 4. Verify new webhook
    console.log('\n4️⃣ Verifying new webhook...');
    const newWebhookInfo = await makeRequest(`https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo`);
    console.log('New webhook info:', JSON.stringify(newWebhookInfo, null, 2));

    // 5. Test webhook endpoint
    console.log('\n5️⃣ Testing webhook endpoint...');
    const healthCheck = await makeRequest(`${WEBHOOK_URL}?action=health`);
    console.log('Health check result:', JSON.stringify(healthCheck, null, 2));

    console.log('\n✅ Webhook setup complete!');
    console.log(`\n📱 Your bot should now receive messages at: ${WEBHOOK_URL}`);
    console.log('\n🧪 Test your bot by sending a message to @Benji_BuddyChip_Bot');

  } catch (error) {
    console.error('❌ Error setting up webhook:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
