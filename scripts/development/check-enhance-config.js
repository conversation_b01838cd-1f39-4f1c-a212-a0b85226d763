#!/usr/bin/env node

/**
 * Configuration Check Script for Enhance Feature
 * 
 * Checks if all required environment variables are properly configured
 * for the enhance feature to work correctly.
 */

const fs = require('fs');
const path = require('path');

// Required environment variables for enhance feature
const REQUIRED_ENV_VARS = {
  // Core AI
  'OPENROUTER_API_KEY': {
    description: 'OpenRouter API key for AI models (Gemini, OpenAI)',
    required: true,
    example: 'sk-or-v1-...'
  },
  
  // AI Tools
  'XAI_API_KEY': {
    description: 'xAI API key for live web search',
    required: false,
    example: 'xai-...'
  },
  'EXA_API_KEY': {
    description: 'Exa API key for semantic search',
    required: false,
    example: 'exa_...'
  },
  'OPENAI_API_KEY': {
    description: 'OpenAI API key for image generation',
    required: false,
    example: 'sk-...'
  },
  
  // Market Intelligence
  'COOKIE_API_KEY': {
    description: 'Cookie.fun API key for crypto market intelligence',
    required: false,
    example: 'cookie_...'
  },
  
  // Database
  'DATABASE_URL': {
    description: 'PostgreSQL database URL',
    required: true,
    example: '********************************/db'
  }
};

function checkEnvironmentVariables() {
  console.log('🔍 Checking Enhance Feature Configuration...\n');
  
  const issues = [];
  const warnings = [];
  
  // Check each required environment variable
  for (const [varName, config] of Object.entries(REQUIRED_ENV_VARS)) {
    const value = process.env[varName];
    
    if (!value) {
      if (config.required) {
        issues.push({
          type: 'MISSING_REQUIRED',
          variable: varName,
          description: config.description,
          example: config.example
        });
      } else {
        warnings.push({
          type: 'MISSING_OPTIONAL',
          variable: varName,
          description: config.description,
          example: config.example
        });
      }
    } else {
      // Check if it looks like a placeholder
      const placeholders = ['your_', 'test-', 'example', 'placeholder'];
      const isPlaceholder = placeholders.some(placeholder => 
        value.toLowerCase().includes(placeholder)
      );
      
      if (isPlaceholder) {
        if (config.required) {
          issues.push({
            type: 'PLACEHOLDER_VALUE',
            variable: varName,
            description: config.description,
            currentValue: value.substring(0, 20) + '...'
          });
        } else {
          warnings.push({
            type: 'PLACEHOLDER_VALUE',
            variable: varName,
            description: config.description,
            currentValue: value.substring(0, 20) + '...'
          });
        }
      } else {
        console.log(`✅ ${varName}: Configured`);
      }
    }
  }
  
  // Report issues
  if (issues.length > 0) {
    console.log('\n❌ Critical Issues Found:');
    issues.forEach(issue => {
      console.log(`\n🔴 ${issue.variable}`);
      console.log(`   Description: ${issue.description}`);
      if (issue.example) {
        console.log(`   Example: ${issue.example}`);
      }
      if (issue.currentValue) {
        console.log(`   Current: ${issue.currentValue}`);
      }
    });
  }
  
  // Report warnings
  if (warnings.length > 0) {
    console.log('\n⚠️ Optional Features Not Configured:');
    warnings.forEach(warning => {
      console.log(`\n🟡 ${warning.variable}`);
      console.log(`   Description: ${warning.description}`);
      console.log(`   Impact: ${getImpactDescription(warning.variable)}`);
      if (warning.example) {
        console.log(`   Example: ${warning.example}`);
      }
    });
  }
  
  // Summary
  console.log('\n📊 Configuration Summary:');
  console.log(`✅ Configured: ${Object.keys(REQUIRED_ENV_VARS).length - issues.length - warnings.length}`);
  console.log(`❌ Critical Issues: ${issues.length}`);
  console.log(`⚠️ Optional Missing: ${warnings.length}`);
  
  if (issues.length === 0) {
    console.log('\n🎉 Enhance feature should work correctly!');
    if (warnings.length > 0) {
      console.log('💡 Some optional features may be limited due to missing API keys.');
    }
  } else {
    console.log('\n🚨 Enhance feature may not work properly due to missing required configuration.');
  }
  
  return {
    success: issues.length === 0,
    issues,
    warnings
  };
}

function getImpactDescription(varName) {
  const impacts = {
    'XAI_API_KEY': 'Live web search will not be available',
    'EXA_API_KEY': 'Semantic knowledge search will not be available', 
    'OPENAI_API_KEY': 'Image generation will not be available',
    'COOKIE_API_KEY': 'Crypto market intelligence will not be available'
  };
  
  return impacts[varName] || 'Feature may be limited';
}

function checkEnvFile() {
  const envPath = path.join(process.cwd(), 'apps/web/.env.local');
  const envExamplePath = path.join(process.cwd(), 'apps/web/.env.example');
  
  console.log('\n📁 Environment File Check:');
  
  if (fs.existsSync(envPath)) {
    console.log('✅ .env.local file exists');
  } else {
    console.log('❌ .env.local file not found');
    if (fs.existsSync(envExamplePath)) {
      console.log('💡 Copy .env.example to .env.local and configure your API keys');
    }
  }
}

function main() {
  console.log('🚀 BuddyChip Enhance Feature Configuration Check\n');
  
  // Load environment variables from .env.local if it exists
  try {
    require('dotenv').config({ path: path.join(process.cwd(), 'apps/web/.env.local') });
  } catch (error) {
    // dotenv not available, continue with system env vars
  }
  
  checkEnvFile();
  const result = checkEnvironmentVariables();
  
  console.log('\n📚 Next Steps:');
  if (result.issues.length > 0) {
    console.log('1. Configure missing required environment variables');
    console.log('2. Restart your development server');
    console.log('3. Test the enhance feature');
  } else {
    console.log('1. Test the enhance feature in the Reply Guy dashboard');
    console.log('2. Monitor the console for any API errors');
    console.log('3. Check response quality and tool usage');
  }
  
  process.exit(result.success ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = { checkEnvironmentVariables };
