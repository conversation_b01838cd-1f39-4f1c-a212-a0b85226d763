#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix import paths in moved script files
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Fixing import paths in moved scripts...');

function updateImportsInFile(filePath, fromLevel, toLevel) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Fix relative imports that need to go up more levels
  const patterns = [
    { from: '"../src/', to: '"../../src/' },
    { from: "'../src/", to: "'../../src/" },
    { from: '"../prisma/', to: '"../../prisma/' },
    { from: "'../prisma/", to: "'../../prisma/" },
    { from: '"../scripts/', to: '"../development/' },
    { from: "'../scripts/", to: "'../development/" },
  ];
  
  patterns.forEach(pattern => {
    if (content.includes(pattern.from)) {
      content = content.replace(new RegExp(pattern.from, 'g'), pattern.to);
      updated = true;
    }
  });
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated imports in ${filePath}`);
  }
}

// Fix database scripts
const databaseScripts = [
  'apps/web/scripts/database/optimize-database-indexes.ts',
  'apps/web/scripts/database/test-account-linking.ts',
  'apps/web/scripts/database/test-telegram-database.ts'
];

databaseScripts.forEach(script => updateImportsInFile(script));

// Fix telegram scripts
const telegramScripts = [
  'apps/web/scripts/telegram/setup-bot-commands.js',
  'apps/web/scripts/telegram/setup-new-telegram-security.ts',
  'apps/web/scripts/telegram/setup-telegram-webhook.js',
  'apps/web/scripts/telegram/test-bot-messaging.js',
  'apps/web/scripts/telegram/test-telegram-logging.ts',
  'apps/web/scripts/telegram/update-webhook-url.ts',
  'apps/web/scripts/telegram/verify-telegram-bot.js'
];

telegramScripts.forEach(script => updateImportsInFile(script));

// Fix testing scripts
const testingScripts = [
  'apps/web/scripts/testing/validate-fixes.ts',
  'apps/web/scripts/testing/validate-refactoring.ts',
  'apps/web/scripts/testing/validate-service-optimization.ts',
  'apps/web/scripts/testing/test-webhook-security.ts'
];

testingScripts.forEach(script => updateImportsInFile(script));

// Fix development scripts
const developmentScripts = [
  'apps/web/scripts/development/debug-start-command.ts',
  'apps/web/scripts/development/setup-refactor.ts',
  'apps/web/scripts/development/validate-refactor.ts',
  'apps/web/scripts/development/quality-check.ts',
  'apps/web/scripts/development/create-baseline.ts'
];

developmentScripts.forEach(script => updateImportsInFile(script));

console.log('🎉 All script imports fixed!');
