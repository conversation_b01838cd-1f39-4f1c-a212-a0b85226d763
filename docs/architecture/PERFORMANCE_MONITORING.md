# Performance Monitoring System

This guide outlines the comprehensive performance monitoring system implemented in BuddyChip, covering client-side performance, server-side metrics, user analytics, and business intelligence.

## Overview

The performance monitoring system provides:
- **Real-time Performance Tracking**: Core Web Vitals, API response times, database queries
- **User Analytics**: Behavior tracking, feature usage, conversion metrics
- **Business Intelligence**: Feature adoption, user engagement, revenue metrics
- **Automated Alerting**: Performance degradation and error notifications
- **Interactive Dashboard**: Real-time monitoring and historical analysis

## Architecture

### Core Components

1. **Enhanced Performance Monitor** (`enhanced-performance-monitor.ts`)
   - Centralized performance data collection
   - Web Vitals monitoring
   - Alert management
   - Real-time metrics aggregation

2. **User Analytics Service** (`user-analytics.ts`)
   - Feature usage tracking
   - Conversion event monitoring
   - User journey analysis
   - Session management

3. **Performance Hooks** (`use-performance-monitoring.ts`)
   - React component performance monitoring
   - API call tracking
   - User interaction monitoring
   - Error boundary integration

4. **Performance Dashboard** (`performance-dashboard.tsx`)
   - Real-time metrics visualization
   - Alert management interface
   - Historical trend analysis
   - Performance optimization insights

## Implementation Guide

### 1. Basic Setup

#### Initialize Performance Monitoring
```typescript
// In your app root or layout
import { enhancedPerformanceMonitor } from "@/lib/enhanced-performance-monitor";
import { analytics } from "@/lib/user-analytics";

// The monitoring starts automatically when imported
// Set user ID when available
analytics.identifyUser(userId);
```

#### Add Page Tracking
```typescript
// In your page components
import { usePageTracking } from "@/hooks/use-performance-monitoring";

export default function MyPage() {
  usePageTracking(); // Automatically tracks page views
  
  return <div>Page content</div>;
}
```

### 2. Feature Usage Tracking

#### Track Feature Usage with Hooks
```typescript
import { useFeatureTracking } from "@/hooks/use-performance-monitoring";
import { FeatureType } from "@/prisma/generated";

export default function AIResponseGenerator() {
  const { startFeature, completeFeature, failFeature } = useFeatureTracking(FeatureType.AI_CALLS);
  
  const generateResponse = async () => {
    startFeature({ prompt: "user prompt" });
    
    try {
      const response = await api.generateResponse();
      completeFeature({ responseLength: response.length });
    } catch (error) {
      failFeature(error.message, { errorType: "api_error" });
    }
  };
  
  return (
    <button onClick={generateResponse}>
      Generate AI Response
    </button>
  );
}
```

#### Manual Feature Tracking
```typescript
import { analytics } from "@/lib/user-analytics";

// Start feature
analytics.featureStarted(FeatureType.AI_CALLS, { 
  model: "gpt-4",
  promptLength: 150 
});

// Complete feature
analytics.featureCompleted(FeatureType.AI_CALLS, 2500, { 
  responseLength: 300,
  tokensUsed: 450 
});

// Track failure
analytics.featureFailed(FeatureType.AI_CALLS, "Rate limit exceeded", {
  retryAfter: 60
});
```

### 3. API Performance Monitoring

#### Track API Calls
```typescript
import { useApiPerformance } from "@/hooks/use-performance-monitoring";

export default function DataComponent() {
  const { trackApiCall } = useApiPerformance();
  
  const fetchData = async () => {
    const data = await trackApiCall(
      "mentions.getLatest",
      () => api.mentions.getLatest({ limit: 20 }),
      { userId, limit: 20 }
    );
    
    return data;
  };
  
  // API calls are automatically timed and tracked
}
```

### 4. User Interaction Tracking

#### Track UI Interactions
```typescript
import { useInteractionTracking } from "@/hooks/use-performance-monitoring";

export default function SearchComponent() {
  const { trackClick, trackSearch } = useInteractionTracking();
  
  const handleSearch = (query: string) => {
    trackSearch(query, results.length, { 
      searchType: "mentions",
      filters: activeFilters 
    });
  };
  
  const handleButtonClick = () => {
    trackClick("generate_response_button", { 
      mentionId,
      buttonPosition: "top" 
    });
  };
  
  return (
    <div>
      <input onChange={(e) => handleSearch(e.target.value)} />
      <button onClick={handleButtonClick}>Generate Response</button>
    </div>
  );
}
```

### 5. Conversion Tracking

#### Track Business Events
```typescript
import { useConversionTracking } from "@/hooks/use-performance-monitoring";

export default function SubscriptionComponent() {
  const { trackSubscription, trackFeatureUnlock } = useConversionTracking();
  
  const handleSubscribe = async (planId: string, price: number) => {
    await processSubscription(planId);
    
    trackSubscription(planId, price, {
      previousPlan: "free",
      upgradeReason: "rate_limit",
      paymentMethod: "stripe"
    });
  };
  
  const handleFeatureUnlock = (feature: string) => {
    trackFeatureUnlock(feature, {
      unlockMethod: "subscription",
      planType: "pro"
    });
  };
}
```

### 6. Error Monitoring

#### Component Error Tracking
```typescript
import { useErrorTracking } from "@/hooks/use-performance-monitoring";

export default function MyComponent() {
  const { trackError } = useErrorTracking("MyComponent");
  
  useEffect(() => {
    try {
      // Component logic
    } catch (error) {
      trackError(error, { context: "useEffect initialization" });
    }
  }, []);
  
  return <div>Component content</div>;
}
```

#### Global Error Boundary
```typescript
import { analytics } from "@/lib/user-analytics";

class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: any) {
    analytics.errorOccurred(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });
  }
}
```

### 7. Performance Dashboard

#### Add Dashboard to Admin Panel
```typescript
import PerformanceDashboard from "@/components/admin/performance-dashboard";

export default function AdminPage() {
  return (
    <div>
      <h1>Admin Panel</h1>
      <PerformanceDashboard />
    </div>
  );
}
```

#### Real-time Metrics Display
```typescript
import { usePerformanceMetrics } from "@/hooks/use-performance-monitoring";

export default function MetricsWidget() {
  const { metrics, loading } = usePerformanceMetrics(30000); // 30 second refresh
  
  if (loading) return <div>Loading metrics...</div>;
  
  return (
    <div>
      <h3>Performance Metrics</h3>
      <p>Average Page Load: {metrics.webVitals.find(v => v.name === "LCP")?.average}ms</p>
      <p>Error Rate: {(metrics.analytics.errorRate * 100).toFixed(1)}%</p>
      <p>Active Users: {metrics.analytics.uniquePages}</p>
    </div>
  );
}
```

## Metrics and KPIs

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s (good), < 4s (needs improvement)
- **FID (First Input Delay)**: < 100ms (good), < 300ms (needs improvement)
- **CLS (Cumulative Layout Shift)**: < 0.1 (good), < 0.25 (needs improvement)
- **FCP (First Contentful Paint)**: < 1.8s (good), < 3s (needs improvement)
- **TTFB (Time to First Byte)**: < 800ms (good), < 1.8s (needs improvement)

### Server Performance
- **API Response Time**: < 200ms (good), < 1s (needs improvement)
- **Database Query Time**: < 100ms (good), < 1s (needs improvement)
- **Error Rate**: < 1% (good), < 5% (acceptable)

### User Engagement
- **Session Duration**: Average time users spend in the app
- **Page Views per Session**: Number of pages viewed per session
- **Feature Adoption Rate**: Percentage of users using specific features
- **Conversion Rate**: Percentage of users completing desired actions

### Business Metrics
- **Feature Usage**: Daily/weekly active users per feature
- **Subscription Conversions**: Free to paid conversion rate
- **Revenue per User**: Average revenue generated per user
- **Retention Rate**: Percentage of users returning after initial visit

## Alerting and Notifications

### Performance Alerts
```typescript
// Automatic alerts for performance degradation
enhancedPerformanceMonitor.createAlert({
  type: "performance",
  severity: "high",
  title: "High API Response Time",
  message: "API response time exceeded 2 seconds",
  metric: "api_response_time",
  currentValue: 2500,
  threshold: 2000,
});
```

### Custom Alert Rules
- **Critical**: > 5s page load time, > 10% error rate
- **High**: > 2s API response time, > 5% error rate
- **Medium**: > 1s database query time, poor Core Web Vitals
- **Low**: Moderate performance degradation

## Data Export and Integration

### Analytics Integration
```typescript
// Send data to external analytics services
const sendToAnalytics = (event: any) => {
  // Google Analytics
  gtag('event', event.type, event.properties);
  
  // Mixpanel
  mixpanel.track(event.type, event.properties);
  
  // Custom analytics endpoint
  fetch('/api/analytics', {
    method: 'POST',
    body: JSON.stringify(event)
  });
};
```

### Data Export
```typescript
// Export performance data
const exportData = async (timeRange: string) => {
  const report = enhancedPerformanceMonitor.getPerformanceReport();
  
  // Convert to CSV or JSON for analysis
  const csv = convertToCSV(report);
  downloadFile(csv, `performance-report-${timeRange}.csv`);
};
```

## Best Practices

### 1. Performance Monitoring
- Monitor Core Web Vitals on all pages
- Track API performance for critical endpoints
- Set up alerts for performance degradation
- Regular performance audits and optimization

### 2. User Analytics
- Track feature usage to understand user behavior
- Monitor conversion funnels to identify drop-off points
- Analyze user journeys to optimize UX
- Respect user privacy and data protection regulations

### 3. Error Tracking
- Implement comprehensive error boundaries
- Track both client-side and server-side errors
- Provide context and metadata for debugging
- Set up notifications for critical errors

### 4. Business Intelligence
- Track metrics aligned with business goals
- Monitor feature adoption and user engagement
- Analyze conversion rates and revenue metrics
- Use data to drive product decisions

## Troubleshooting

### Common Issues
1. **High Memory Usage**: Check for memory leaks in event listeners
2. **Performance Impact**: Ensure monitoring doesn't affect app performance
3. **Data Privacy**: Implement proper data anonymization
4. **Alert Fatigue**: Fine-tune alert thresholds to reduce noise

### Debugging Tools
```bash
# Check performance metrics
node -e "
import { enhancedPerformanceMonitor } from './src/lib/enhanced-performance-monitor.js';
console.log(enhancedPerformanceMonitor.getPerformanceReport());
"

# Monitor real-time events
ENABLE_PERFORMANCE_LOGS=true npm run dev
```
