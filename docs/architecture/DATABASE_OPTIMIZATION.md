# Database Optimization Guide

This guide outlines the database optimization improvements implemented in BuddyChip, including indexing strategies, query optimization, and performance monitoring.

## Overview

The database optimization system provides:
- **Intelligent Indexing**: Optimized indexes for common query patterns
- **Query Optimization**: Efficient query builders and cursor pagination
- **Batch Operations**: High-performance bulk operations
- **Performance Monitoring**: Real-time query performance tracking
- **Connection Pooling**: Optimized database connections

## Key Components

### 1. Database Optimizer (`database-optimizer.ts`)

Provides optimized query patterns and batch operations:

```typescript
import { databaseOptimizer } from "@/lib/database-optimizer";

// Optimized mention queries
const mentions = await databaseOptimizer.queries.getMentionsOptimized(userId, {
  limit: 20,
  includeResponses: true,
  includeAccount: true,
});

// Batch operations
await databaseOptimizer.batch.batchCreate("mention", mentionData, {
  batchSize: 100,
  skipDuplicates: true,
});

// Health monitoring
const metrics = await databaseOptimizer.health.checkPerformanceMetrics();
```

### 2. Query Optimizer (`query-optimizer.ts`)

Advanced query optimization utilities:

```typescript
import { queryOptimizer } from "@/lib/query-optimizer";

// Cursor-based pagination
const cursor = queryOptimizer.cursor.createCursor(new Date(), "mention-id");
const whereClause = queryOptimizer.cursor.buildWhereClause(cursor);

// Optimized query builders
const mentionQuery = queryOptimizer.builders.buildMentionQuery({
  userId,
  limit: 20,
  includeResponses: true,
});

// Data loaders (prevent N+1 queries)
const responses = await queryOptimizer.loaders.batchLoadResponses(mentionIds);
```

### 3. Index Optimization Script (`optimize-database-indexes.ts`)

Automated index optimization:

```bash
# Run index optimization
bunx tsx scripts/optimize-database-indexes.ts

# High priority indexes only
bunx tsx scripts/optimize-database-indexes.ts high

# Analyze performance
bunx tsx scripts/optimize-database-indexes.ts analyze
```

## Optimization Strategies

### 1. Indexing Strategy

#### High Priority Indexes
```sql
-- User mentions with date sorting and archive filtering
CREATE INDEX "idx_mentions_user_date_optimized" 
ON "mentions" ("userId", "mentionedAt" DESC, "archived");

-- Account mentions with processing status
CREATE INDEX "idx_mentions_account_date_optimized" 
ON "mentions" ("accountId", "mentionedAt" DESC, "processed");

-- Rate limiting queries
CREATE INDEX "idx_usage_log_rate_limiting" 
ON "UsageLog" ("userId", "feature", "billingPeriod", "createdAt" DESC);
```

#### Specialized Indexes
```sql
-- Full-text search on content
CREATE INDEX "idx_mentions_content_search" 
ON "mentions" USING gin ("content");

-- Keyword array searches
CREATE INDEX "idx_mentions_keywords_search" 
ON "mentions" USING gin ("keywords");

-- Partial index for bullish scores
CREATE INDEX "idx_mentions_bullish_score_filtered" 
ON "mentions" ("bullishScore" DESC) 
WHERE "bullishScore" IS NOT NULL AND "bullishScore" > 0;
```

### 2. Query Optimization Patterns

#### Before (Inefficient)
```typescript
// ❌ N+1 query problem
const mentions = await prisma.mention.findMany({ where: { userId } });
for (const mention of mentions) {
  const responses = await prisma.aiResponse.findMany({
    where: { mentionId: mention.id }
  });
}

// ❌ Over-fetching data
const user = await prisma.user.findUnique({
  where: { id },
  include: { 
    monitoredAccounts: { 
      include: { mentions: true } // Potentially huge dataset
    }
  }
});
```

#### After (Optimized)
```typescript
// ✅ Batch loading
const mentions = await databaseOptimizer.queries.getMentionsOptimized(userId);
const responses = await queryOptimizer.loaders.batchLoadResponses(
  mentions.map(m => m.id)
);

// ✅ Selective fetching with pagination
const user = await databaseOptimizer.queries.getUserWithPlanOptimized(userId);
const accounts = await queryOptimizer.builders.buildAccountWithStatsQuery(userId);
```

### 3. Cursor-Based Pagination

#### Efficient Large Dataset Navigation
```typescript
// Create cursor from timestamp + ID
const cursor = queryOptimizer.cursor.createCursor(lastMention.mentionedAt, lastMention.id);

// Build optimized where clause
const whereClause = queryOptimizer.cursor.buildWhereClause(cursor, "mentionedAt");

// Query with cursor
const nextMentions = await prisma.mention.findMany({
  where: {
    userId,
    ...whereClause,
  },
  orderBy: [
    { mentionedAt: "desc" },
    { id: "desc" },
  ],
  take: 20,
});
```

### 4. Batch Operations

#### High-Performance Bulk Operations
```typescript
// Batch create with automatic chunking
const result = await databaseOptimizer.batch.batchCreate("mention", mentionData, {
  batchSize: 100,
  delayMs: 10,
  skipDuplicates: true,
});

// Batch update with transactions
const updates = mentions.map(mention => ({
  where: { id: mention.id },
  data: { processed: true },
}));

await databaseOptimizer.batch.batchUpdate("mention", updates);
```

## Performance Monitoring

### 1. Real-Time Query Monitoring

```typescript
// Monitor query performance
const result = await queryOptimizer.optimizer.monitorQuery(
  "mentions.getLatest",
  () => prisma.mention.findMany({ /* query */ }),
  {
    warnThreshold: 500,
    errorThreshold: 1000,
  }
);
```

### 2. Database Health Checks

```typescript
// Check performance metrics
const metrics = await databaseOptimizer.health.checkPerformanceMetrics();
console.log("Active connections:", metrics.connectionCount);
console.log("Slow queries:", metrics.slowQueries);

// Analyze table statistics
const stats = await databaseOptimizer.health.analyzeTableStats("mentions");

// Get optimization suggestions
const suggestions = await databaseOptimizer.health.suggestIndexOptimizations();
```

## Connection Pooling

### 1. Optimized Connection Configuration

```typescript
// Automatic connection optimization
const client = createPrismaClient({
  instanceId: "web-app",
  connectionPool: {
    poolSize: 10,
    connectionTimeout: 30000,
    idleTimeout: 60000,
  },
});
```

### 2. Environment Configuration

```env
# Pooled connection for app queries
DATABASE_URL="********************************/db?pgbouncer=true&connection_limit=10"

# Direct connection for migrations
DIRECT_URL="********************************/db"
```

## Migration Guide

### 1. Update Existing Queries

Replace manual queries with optimized patterns:

```typescript
// Before
const mentions = await prisma.mention.findMany({
  where: { userId },
  include: { responses: true, account: true },
});

// After
const mentions = await databaseOptimizer.queries.getMentionsOptimized(userId, {
  includeResponses: true,
  includeAccount: true,
});
```

### 2. Implement Batch Operations

Replace individual operations with batch processing:

```typescript
// Before
for (const mention of mentions) {
  await prisma.mention.update({
    where: { id: mention.id },
    data: { processed: true },
  });
}

// After
await databaseOptimizer.batch.batchUpdate("mention", 
  mentions.map(m => ({ where: { id: m.id }, data: { processed: true } }))
);
```

### 3. Add Performance Monitoring

Wrap critical queries with monitoring:

```typescript
// Before
const result = await prisma.mention.findMany({ /* query */ });

// After
const result = await queryOptimizer.optimizer.monitorQuery(
  "mentions.findMany",
  () => prisma.mention.findMany({ /* query */ })
);
```

## Performance Benchmarks

### Expected Improvements

- **Query Performance**: 60-80% faster for common queries
- **Memory Usage**: 40% reduction with optimized selects
- **Connection Efficiency**: 90% reduction in connection timeouts
- **Batch Operations**: 10x faster for bulk operations

### Monitoring Thresholds

- **Fast Query**: < 100ms
- **Moderate Query**: 100-500ms (warning)
- **Slow Query**: 500-1000ms (attention needed)
- **Critical Query**: > 1000ms (immediate optimization required)

## Best Practices

1. **Always use selective queries** - Only fetch needed fields
2. **Implement cursor pagination** - For large datasets
3. **Batch related operations** - Reduce database round trips
4. **Monitor query performance** - Track and optimize slow queries
5. **Use appropriate indexes** - Match query patterns
6. **Leverage connection pooling** - Optimize connection usage
7. **Regular health checks** - Monitor database performance

## Troubleshooting

### Common Issues

1. **Slow Queries**: Check indexes and query structure
2. **Connection Timeouts**: Verify connection pool configuration
3. **High Memory Usage**: Review query selects and pagination
4. **N+1 Queries**: Implement data loaders

### Debugging Tools

```bash
# Analyze query performance
bunx tsx scripts/optimize-database-indexes.ts analyze

# Check database health
node -e "
import { databaseOptimizer } from './src/lib/database-optimizer.js';
databaseOptimizer.health.checkPerformanceMetrics().then(console.log);
"

# Monitor slow queries
ENABLE_PRISMA_QUERY_LOGS=true npm run dev
```
