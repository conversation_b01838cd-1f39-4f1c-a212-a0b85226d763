# Rate Limiting Migration Guide

## Overview

This guide provides step-by-step instructions for migrating from manual rate limiting patterns to the consolidated rate limiting middleware system in BuddyChip's tRPC API.

## Migration Process

### Step 1: Identify Current Rate Limiting Patterns

Before migrating, identify which patterns your endpoints currently use:

#### Pattern A: Manual Rate Limiting with Usage Tracking
```typescript
// OLD PATTERN - Manual implementation
const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
if (!rateLimit.allowed) {
  throw new TRPCError({
    code: "TOO_MANY_REQUESTS",
    message: `Rate limit exceeded. ${rateLimit.remaining} remaining.`,
  });
}

// ... operation logic ...

await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);
```

#### Pattern B: Conditional Rate Limiting (Cache-Aware)
```typescript
// OLD PATTERN - Manual conditional implementation
const cached = await getFromCache();
if (cached) {
  return cached;
}

const rateLimit = await checkRateLimit(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
if (!rateLimit.allowed) {
  throw new TRPCError({ code: "TOO_MANY_REQUESTS" });
}

const result = await fetchFromAPI();
await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
```

#### Pattern C: Feature Access Validation
```typescript
// OLD PATTERN - Manual feature validation
const canUse = await canUserUseFeature(ctx.userId, FeatureType.IMAGE_GENERATIONS);
if (!canUse.allowed) {
  throw new TRPCError({
    code: "FORBIDDEN",
    message: canUse.reason,
  });
}
```

### Step 2: Import New Middleware

Update your imports to include the new middleware patterns:

```typescript
// Before
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { checkRateLimit, recordUsage } from "../lib/db-utils";

// After
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { 
  createFeatureProcedure, 
  createMonitoredProcedure, 
  createCacheAwareFeatureProcedure,
  createEnhancedFeatureProcedure,
  handleTRPCError 
} from "../lib/trpc-middleware";
```

### Step 3: Choose the Right Middleware Pattern

#### For Standard Rate Limited Operations
```typescript
// Before
const myEndpoint = protectedProcedure
  .input(myInputSchema)
  .mutation(async ({ ctx, input }) => {
    const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
    if (!rateLimit.allowed) {
      throw new TRPCError({ code: "TOO_MANY_REQUESTS" });
    }
    
    const result = await doOperation(input);
    await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);
    return result;
  });

// After
const myEndpoint = createFeatureProcedure(FeatureType.AI_CALLS, {
  requestedAmount: 1,
  operationName: "myOperation",
})
  .input(myInputSchema)
  .mutation(async ({ ctx, input }) => {
    const result = await doOperation(input);
    return result;
  });
```

#### For Cached Operations
```typescript
// Before
const myCachedEndpoint = protectedProcedure
  .input(myInputSchema)
  .query(async ({ ctx, input }) => {
    const cached = await getFromCache(input);
    if (cached) {
      return cached;
    }
    
    const rateLimit = await checkRateLimit(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
    if (!rateLimit.allowed) {
      throw new TRPCError({ code: "TOO_MANY_REQUESTS" });
    }
    
    const result = await fetchFromAPI(input);
    await setCache(input, result);
    await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
    return result;
  });

// After
const myCachedEndpoint = createCacheAwareFeatureProcedure(FeatureType.COOKIE_API_CALLS, {
  requestedAmount: 1,
  operationName: "myCachedOperation",
})
  .input(myInputSchema)
  .query(async ({ ctx, input }) => {
    const cached = await getFromCache(input);
    if (cached) {
      return cached;
    }
    
    // Set cache miss context for middleware
    ctx.cacheMiss = true;
    
    const result = await fetchFromAPI(input);
    await setCache(input, result);
    return result;
  });
```

#### For Non-Consuming Operations
```typescript
// Before
const myStatsEndpoint = protectedProcedure
  .query(async ({ ctx }) => {
    return await getUserStats(ctx.userId);
  });

// After
const myStatsEndpoint = createMonitoredProcedure("getUserStats")
  .query(async ({ ctx }) => {
    return await getUserStats(ctx.userId);
  });
```

### Step 4: Update Error Handling

Replace manual error handling with the consolidated error handler:

```typescript
// Before
try {
  const result = await doOperation(input);
  return result;
} catch (error) {
  console.error("Operation failed:", error);
  
  if (error instanceof TRPCError) {
    throw error;
  }
  
  throw new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: "Operation failed",
  });
}

// After
try {
  const result = await doOperation(input);
  return result;
} catch (error) {
  handleTRPCError(error, "operationName", {
    userId: ctx.userId,
    input: input,
  });
}
```

### Step 5: Remove Manual Imports

Remove imports that are no longer needed:

```typescript
// Remove these imports after migration
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { canUserUseFeature } from "../lib/user-service";
```

## Common Migration Scenarios

### Scenario 1: AI Operations (Benji Router)

**Before**:
```typescript
generateResponse: protectedProcedure
  .input(responseSchema)
  .mutation(async ({ ctx, input }) => {
    // Manual rate limiting
    const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
    if (!rateLimit.allowed) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: `AI calls limit exceeded. ${rateLimit.remaining} remaining.`,
      });
    }

    try {
      const result = await generateAIResponse(input);
      await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);
      return result;
    } catch (error) {
      console.error("AI generation failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to generate response",
      });
    }
  });
```

**After**:
```typescript
generateResponse: createFeatureProcedure(FeatureType.AI_CALLS, {
  requestedAmount: 1,
  operationName: "generateResponse",
})
  .input(responseSchema)
  .mutation(async ({ ctx, input }) => {
    try {
      const result = await generateAIResponse(input);
      return result;
    } catch (error) {
      handleTRPCError(error, "generateResponse", {
        userId: ctx.userId,
        inputLength: input.content.length,
      });
    }
  });
```

### Scenario 2: External API Operations (Crypto Router)

**Before**:
```typescript
getTrendingProjects: protectedProcedure
  .input(trendingSchema)
  .query(async ({ ctx, input }) => {
    // Check cache first
    const cached = await cryptoCache.getCachedTrending(input.sector);
    if (cached) {
      return { success: true, data: cached.data, cached: true };
    }

    // Manual rate limiting for API calls
    const rateLimit = await checkRateLimit(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
    if (!rateLimit.allowed) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: `API calls limit exceeded. ${rateLimit.remaining} remaining.`,
      });
    }

    try {
      const projects = await cookieClient.getTrendingProjects(input.sector);
      await cryptoCache.cacheTrending(projects, input.sector);
      await recordUsage(ctx.userId, FeatureType.COOKIE_API_CALLS, 1);
      
      return { success: true, data: projects, cached: false };
    } catch (error) {
      console.error("Failed to fetch trending projects:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch trending projects",
      });
    }
  });
```

**After**:
```typescript
getTrendingProjects: createFeatureProcedure(FeatureType.COOKIE_API_CALLS, {
  requestedAmount: 1,
  operationName: "getTrendingProjects",
})
  .input(trendingSchema)
  .query(async ({ ctx, input }) => {
    try {
      // Check cache first
      const cached = await cryptoCache.getCachedTrending(input.sector);
      if (cached) {
        return { success: true, data: cached.data, cached: true };
      }

      // Fetch from API (middleware handles rate limiting and usage tracking)
      const projects = await cookieClient.getTrendingProjects(input.sector);
      await cryptoCache.cacheTrending(projects, input.sector);
      
      return { success: true, data: projects, cached: false };
    } catch (error) {
      handleTRPCError(error, "getTrendingProjects", {
        sector: input.sector,
        timeframe: input.timeframe,
      });
    }
  });
```

### Scenario 3: Account Management (Accounts Router)

**Before**:
```typescript
addAccount: protectedProcedure
  .input(addAccountSchema)
  .mutation(async ({ ctx, input }) => {
    // Manual feature validation
    const canUse = await canUserUseFeature(ctx.userId, FeatureType.MONITORED_ACCOUNTS);
    if (!canUse.allowed) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Account limit exceeded. ${canUse.reason}`,
      });
    }

    try {
      const account = await createMonitoredAccount(ctx.userId, input);
      // Note: MONITORED_ACCOUNTS doesn't use usage tracking - it counts actual records
      return { success: true, account };
    } catch (error) {
      console.error("Failed to add account:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to add account",
      });
    }
  });
```

**After**:
```typescript
addAccount: createEnhancedFeatureProcedure(FeatureType.MONITORED_ACCOUNTS, {
  requestedAmount: 1,
  operationName: "addAccount",
  requireFeatureAccess: true,
})
  .input(addAccountSchema)
  .mutation(async ({ ctx, input }) => {
    try {
      const account = await createMonitoredAccount(ctx.userId, input);
      return { success: true, account };
    } catch (error) {
      handleTRPCError(error, "addAccount", {
        handle: input.handle,
        userId: ctx.userId,
      });
    }
  });
```

### Scenario 4: Bulk Operations

**Before**:
```typescript
processBulkItems: protectedProcedure
  .input(bulkSchema)
  .mutation(async ({ ctx, input }) => {
    const itemCount = input.items.length;
    
    // Manual bulk rate limiting
    const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, itemCount);
    if (!rateLimit.allowed) {
      throw new TRPCError({
        code: "TOO_MANY_REQUESTS",
        message: `Bulk operation would exceed limit. Requested ${itemCount} but only ${rateLimit.remaining} remaining.`,
      });
    }

    try {
      const results = await Promise.all(
        input.items.map(item => processItem(item))
      );
      await recordUsage(ctx.userId, FeatureType.AI_CALLS, itemCount);
      return { success: true, results };
    } catch (error) {
      console.error("Bulk processing failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Bulk processing failed",
      });
    }
  });
```

**After**:
```typescript
processBulkItems: protectedProcedure
  .use(createBulkOperationMiddleware(
    FeatureType.AI_CALLS,
    (input) => input.items.length,
    "processBulkItems"
  ))
  .input(bulkSchema)
  .mutation(async ({ ctx, input }) => {
    try {
      const results = await Promise.all(
        input.items.map(item => processItem(item))
      );
      return { success: true, results };
    } catch (error) {
      handleTRPCError(error, "processBulkItems", {
        itemCount: input.items.length,
        userId: ctx.userId,
      });
    }
  });
```

## Migration Checklist

### Before Migration
- [ ] Identify all endpoints with manual rate limiting
- [ ] Document current rate limiting patterns
- [ ] Verify test coverage for affected endpoints
- [ ] Review subscription plan configurations

### During Migration
- [ ] Update imports to include new middleware
- [ ] Choose appropriate middleware pattern for each endpoint
- [ ] Replace manual rate limiting with middleware
- [ ] Update error handling to use `handleTRPCError`
- [ ] Remove unused imports
- [ ] Update any custom rate limiting logic

### After Migration
- [ ] Run all tests to verify functionality
- [ ] Monitor rate limiting behavior in development
- [ ] Verify usage tracking accuracy
- [ ] Check performance impact
- [ ] Update documentation

## Testing Migration

### Unit Tests
```typescript
// Test rate limiting middleware
describe("createFeatureProcedure", () => {
  it("should enforce rate limits", async () => {
    // Setup user with limited plan
    const user = await createTestUser({ plan: "reply-guy" });
    
    // Exceed rate limit
    const requests = Array(101).fill(null).map(() => 
      caller.ai.generateResponse({ content: "test" })
    );
    
    await expect(Promise.all(requests)).rejects.toThrow("TOO_MANY_REQUESTS");
  });
});
```

### Integration Tests
```typescript
// Test end-to-end rate limiting
describe("Rate Limiting Integration", () => {
  it("should track usage across multiple operations", async () => {
    const user = await createTestUser({ plan: "pro-plan" });
    
    // Make multiple API calls
    await caller.ai.generateResponse({ content: "test1" });
    await caller.ai.generateResponse({ content: "test2" });
    
    // Check usage tracking
    const usage = await caller.benji.getUsageStats();
    expect(usage.usage.AI_CALLS).toBe(2);
  });
});
```

## Performance Considerations

### Before Migration
- Manual rate limiting scattered throughout codebase
- Inconsistent error handling
- No centralized monitoring
- Potential for rate limiting bugs

### After Migration
- Centralized rate limiting logic
- Consistent error handling and monitoring
- Reduced code duplication
- Better security event logging
- Improved performance monitoring

## Troubleshooting Common Issues

### Issue 1: Rate Limit False Positives
**Problem**: Users getting rate limited when they shouldn't be
**Solution**: Check billing period calculation and usage log cleanup

### Issue 2: Cache Miss Issues
**Problem**: Cache-aware middleware not working correctly
**Solution**: Ensure `ctx.cacheMiss = true` is set properly

### Issue 3: Missing Usage Tracking
**Problem**: Usage not being recorded
**Solution**: Verify middleware is applied correctly and operation completes successfully

### Issue 4: Performance Degradation
**Problem**: Slower response times after migration
**Solution**: Check database query performance and middleware overhead

## Best Practices

1. **Always use `handleTRPCError`** for consistent error handling
2. **Choose the most specific middleware pattern** for your use case
3. **Test rate limiting behavior** in development
4. **Monitor usage patterns** after migration
5. **Document any custom middleware** you create
6. **Keep middleware configurations** close to the endpoint definitions

## Additional Resources

- [Rate Limiting Architecture Documentation](./rate-limiting-architecture.md)
- [Middleware API Reference](../src/lib/trpc-middleware.ts)
- [Database Utils Reference](../src/lib/db-utils.ts)
- [Security Utils Reference](../src/lib/security-utils.ts)

This migration guide provides comprehensive instructions for moving from manual rate limiting to the consolidated middleware system. Follow the steps carefully and test thoroughly to ensure a smooth transition.