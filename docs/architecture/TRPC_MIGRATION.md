# tRPC Architecture Improvements Migration Guide

This guide outlines the migration from the current tRPC setup to the new unified middleware and schema system.

## Overview

The tRPC improvements consolidate:
- Middleware patterns (rate limiting, usage tracking, performance monitoring)
- Schema validation and common input patterns
- Error handling and security logging
- Router utilities and CRUD patterns

## Key Changes

### 1. Unified Middleware System

#### Before (Duplicate Middleware in Each Router)
```typescript
// In each router file
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { performanceMonitor } from "../lib/performance-monitor";

export const someRouter = createTRPCRouter({
  someOperation: protectedProcedure
    .input(z.object({ /* validation */ }))
    .mutation(async ({ input, ctx }) => {
      // Manual rate limiting
      const rateLimitResult = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS);
      if (!rateLimitResult.allowed) {
        throw new TRPCError({ code: "TOO_MANY_REQUESTS", message: "Rate limit exceeded" });
      }

      try {
        // Manual performance tracking
        const startTime = performance.now();
        const result = await someOperation();
        const duration = performance.now() - startTime;
        
        // Manual usage recording
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);
        
        return result;
      } catch (error) {
        // Manual error handling
        console.error("Error:", error);
        throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Operation failed" });
      }
    }),
});
```

#### After (Unified Middleware)
```typescript
// Import unified middleware
import { createFeatureProcedure, handleTRPCError } from "../lib/trpc-middleware";
import { aiSchemas } from "../lib/trpc-schemas";

export const someRouter = createTRPCRouter({
  someOperation: createFeatureProcedure(FeatureType.AI_CALLS, {
    operationName: "someOperation",
    requestedAmount: 1,
  })
    .input(aiSchemas.generateResponse)
    .mutation(async ({ input, ctx }) => {
      try {
        // Middleware automatically handles:
        // - Rate limiting
        // - Performance monitoring
        // - Usage tracking
        // - Security logging
        const result = await someOperation();
        return result;
      } catch (error) {
        handleTRPCError(error, "some operation", { input });
      }
    }),
});
```

### 2. Common Schema System

#### Before (Duplicate Schemas)
```typescript
// Repeated in multiple routers
.input(
  z.object({
    limit: z.number().min(1).max(50).default(10),
    cursor: z.string().optional(),
    handle: z.string().min(1).regex(/^@?[a-zA-Z0-9_]+$/),
    // ... more duplicate validation
  })
)
```

#### After (Shared Schemas)
```typescript
// Import from common schemas
import { commonInputSchemas, mentionSchemas } from "../lib/trpc-schemas";

.input(
  schemaUtils.withPagination(mentionSchemas.mentionFilters)
)
```

### 3. Standardized Error Handling

#### Before (Inconsistent Error Handling)
```typescript
} catch (error) {
  console.error("Error:", error);
  if (error.message.includes("rate limit")) {
    throw new TRPCError({ code: "TOO_MANY_REQUESTS", message: error.message });
  }
  throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Failed" });
}
```

#### After (Unified Error Handling)
```typescript
} catch (error) {
  handleTRPCError(error, "operation name", { context });
}
```

## Migration Steps

### Step 1: Update Router Imports

Replace individual middleware imports with unified system:

```typescript
// Remove these imports
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { performanceMonitor } from "../lib/performance-monitor";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

// Add these imports
import { 
  createFeatureProcedure, 
  createMonitoredProcedure, 
  handleTRPCError 
} from "../lib/trpc-middleware";
import { 
  commonInputSchemas, 
  [entityName]Schemas, 
  schemaUtils 
} from "../lib/trpc-schemas";
```

### Step 2: Convert Procedures

#### For Feature-Gated Operations (AI calls, image generation, etc.)
```typescript
// Old
someOperation: protectedProcedure
  .input(/* custom schema */)
  .mutation(async ({ input, ctx }) => {
    // Manual middleware logic
  })

// New
someOperation: createFeatureProcedure(FeatureType.AI_CALLS, {
  operationName: "entity.operation",
  requestedAmount: 1,
})
  .input(entitySchemas.operationInput)
  .mutation(async ({ input, ctx }) => {
    // Clean business logic only
  })
```

#### For Simple Queries (no rate limiting needed)
```typescript
// Old
getOperation: protectedProcedure
  .input(/* schema */)
  .query(async ({ input, ctx }) => {
    // Manual error handling
  })

// New
getOperation: createMonitoredProcedure("entity.get")
  .input(commonInputSchemas.pagination)
  .query(async ({ input, ctx }) => {
    // Clean business logic only
  })
```

### Step 3: Update Input Schemas

Replace custom validation with common schemas:

```typescript
// Old
.input(
  z.object({
    limit: z.number().min(1).max(50).default(10),
    twitterHandle: z.string().regex(/^@?[a-zA-Z0-9_]+$/),
    // ... more validation
  })
)

// New
.input(
  schemaUtils.withPagination(
    commonInputSchemas.twitterHandle.pick({ handle: true })
  )
)
```

### Step 4: Standardize Error Handling

Replace manual error handling:

```typescript
// Old
} catch (error) {
  console.error("Error:", error);
  if (/* specific condition */) {
    throw new TRPCError({ /* specific error */ });
  }
  throw new TRPCError({ code: "INTERNAL_SERVER_ERROR", message: "Failed" });
}

// New
} catch (error) {
  handleTRPCError(error, "operation description", { 
    userId: ctx.userId, 
    input 
  });
}
```

## Router-Specific Migration Examples

### Mentions Router
```typescript
// Before
export const mentionsRouter = createTRPCRouter({
  getLatest: protectedProcedure
    .input(z.object({ limit: z.number().min(1).max(50).default(10) }))
    .query(async ({ input, ctx }) => {
      // Manual performance tracking and error handling
    }),
});

// After
export const mentionsRouter = createTRPCRouter({
  getLatest: createMonitoredProcedure("mentions.getLatest")
    .input(commonInputSchemas.pagination.pick({ limit: true }))
    .query(async ({ input, ctx }) => {
      // Clean business logic with automatic monitoring
    }),
});
```

### Benji Router (AI Operations)
```typescript
// Before
generateResponse: protectedProcedure
  .input(/* complex validation */)
  .mutation(async ({ input, ctx }) => {
    // Manual rate limiting, usage tracking, performance monitoring
  }),

// After
generateResponse: createFeatureProcedure(FeatureType.AI_CALLS, {
  operationName: "benji.generateResponse",
  requestedAmount: 1,
})
  .input(aiSchemas.generateResponse)
  .mutation(async ({ input, ctx }) => {
    // Clean AI logic with automatic middleware
  }),
```

## Benefits

1. **Consistency**: All routers follow the same patterns
2. **Maintainability**: Middleware changes apply to all routers automatically
3. **Performance**: Centralized monitoring and optimization
4. **Security**: Consistent rate limiting and logging
5. **Developer Experience**: Less boilerplate, better TypeScript support
6. **Testing**: Easier to test with standardized patterns

## Validation Checklist

- [ ] All routers use unified middleware system
- [ ] Common schemas replace duplicate validation
- [ ] Error handling uses `handleTRPCError`
- [ ] Feature-gated operations use `createFeatureProcedure`
- [ ] Simple queries use `createMonitoredProcedure`
- [ ] Performance monitoring is automatic
- [ ] Rate limiting is consistent across routers
- [ ] Security logging is standardized
- [ ] TypeScript types are properly inferred
- [ ] Tests pass with new middleware system

## Next Steps

1. Complete router migrations one by one
2. Update client-side tRPC usage if needed
3. Monitor performance improvements
4. Add additional middleware as needed
5. Consider router consolidation opportunities
